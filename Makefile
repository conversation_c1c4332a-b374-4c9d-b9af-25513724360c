.PHONY: build build-release build-wasm clean fmt lint test test-real-world collect-shreds test-with-data help

# Default target
help:
	@echo "Shredstream Decoder - Available Commands:"
	@echo ""
	@echo "Build Commands:"
	@echo "  build          - Build the project in debug mode"
	@echo "  build-release  - Build the project in release mode"
	@echo "  build-wasm     - Build WASM package with TypeScript definitions"
	@echo ""
	@echo "Development Commands:"
	@echo "  fmt            - Format code with rustfmt"
	@echo "  lint           - Run clippy linter"
	@echo "  clean          - Clean build artifacts"
	@echo ""
	@echo "Testing Commands:"
	@echo "  test           - Run basic tests"
	@echo "  test-real-world - Test decoder on real shred samples"
	@echo "  collect-shreds - Collect shred data for testing"
	@echo "  test-with-data - Collect data and run real-world tests (combined)"
	@echo ""
	@echo "Environment:"
	@echo "  Make sure to set SHREDSTREAM_ENDPOINT in .env file"

# Build commands
build:
	cargo build

build-release:
	cargo build --release

build-wasm:
	@echo "Building WASM package with TypeScript definitions..."
	wasm-pack build --target bundler --out-dir pkg
	@echo "✅ Build complete!"
	@echo "📁 Output directory: pkg/"
	@echo "📄 TypeScript definitions: pkg/shredstream_decoder.d.ts"

# Development commands
fmt:
	cargo fmt

lint:
	cargo clippy -- -D warnings

clean:
	cargo clean
	rm -rf pkg/
	rm -rf target/

# Testing commands
test:
	cargo test

test-real-world:
	@echo "🌍 Testing Decoder on Real-World Solana Data"
	@echo "==========================================="
	@echo ""
	@SHRED_COUNT=$$(ls tests/data/shred_*.bin 2>/dev/null | wc -l | tr -d ' '); \
	if [ "$$SHRED_COUNT" -lt 10 ]; then \
		echo "❌ Found only $$SHRED_COUNT shred files (need at least 10)"; \
		echo "   Run 'make collect-shreds' first to collect test data"; \
		exit 1; \
	fi
	@echo "📊 Found $$SHRED_COUNT shred samples - running tests..."
	cargo test real_world_test -- --nocapture
	@echo "✅ Real-world testing completed!"

collect-shreds:
	@echo "Starting Solana Shred Data Collection..."
	@echo "This will collect shred samples from the shredstream."
	@echo ""
	@if [ ! -f .env ]; then \
		echo "❌ .env file not found. Please create it with SHREDSTREAM_ENDPOINT."; \
		exit 1; \
	fi
	cargo run --bin shred_collector --features "tokio,solana-stream-sdk,dotenvy"
	@echo ""
	@echo "Collection completed. Check tests/data/ directory for the collected data."

test-with-data:
	@echo "🚀 Combined Data Collection and Real-World Testing"
	@echo "=================================================="
	@echo ""
	@echo "Step 1: Collecting fresh shred data..."
	@$(MAKE) collect-shreds
	@echo ""
	@echo "Step 2: Running real-world tests on collected data..."
	@$(MAKE) test-real-world
	@echo ""
	@echo "✅ Combined test completed successfully!"
