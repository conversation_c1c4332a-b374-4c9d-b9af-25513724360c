mod common;

#[cfg(test)]
mod helper_functions_tests {
    use super::common::{
        assertions::{assert_binary_compatible, assert_byte_array_equal, assert_no_panic, assert_roundtrip_compatible},
        compatibility_validators::validators::{
            compare_byte_arrays, generate_compatibility_report, validate_entry_binary_compatibility,
            CompatibilityResult,
        },
        fixtures::fixtures::{get_edge_case_data, get_known_good_entry_bytes, get_small_entries},
        mock_data::generators::{
            generate_edge_case_entries, generate_empty_entry, generate_malformed_data_samples,
            generate_mock_entry_with_transactions, generate_single_transaction_entry, generate_various_message_types,
        },
        reference_implementations::reference::{
            create_sample_entry_reference, deserialize_entry_reference, serialize_entry_reference,
        },
        test_data_loader::data_loader::{get_edge_case_samples, get_test_samples, ShredDataset},
    };

    #[test]
    fn test_reference_implementations_basic() {
        let sample_entry = create_sample_entry_reference();
        assert_eq!(sample_entry.num_hashes, 1);
        assert_eq!(sample_entry.transactions.len(), 0);

        let serialized = serialize_entry_reference(&sample_entry).unwrap();
        let deserialized = deserialize_entry_reference(&serialized).unwrap();

        assert_eq!(sample_entry.num_hashes, deserialized.num_hashes);
        assert_eq!(sample_entry.hash, deserialized.hash);
        assert_eq!(sample_entry.transactions.len(), deserialized.transactions.len());
    }

    #[test]
    fn test_compatibility_validators_basic() {
        let data1 = vec![1, 2, 3, 4];
        let data2 = vec![1, 2, 3, 4];
        let data3 = vec![1, 2, 3, 5];

        assert!(validate_entry_binary_compatibility(&data1, &data2));
        assert!(!validate_entry_binary_compatibility(&data1, &data3));

        assert_eq!(compare_byte_arrays(&data1, &data2), None);
        assert_eq!(compare_byte_arrays(&data1, &data3), Some(3));
    }

    #[test]
    fn test_mock_data_generators() {
        let entry = generate_mock_entry_with_transactions(3);
        assert_eq!(entry.transactions.len(), 3);
        assert_eq!(entry.num_hashes, 1);

        let empty_entry = generate_empty_entry();
        assert_eq!(empty_entry.transactions.len(), 0);
        assert_eq!(empty_entry.num_hashes, 0);

        let single_tx_entry = generate_single_transaction_entry();
        assert_eq!(single_tx_entry.transactions.len(), 1);
        assert_eq!(single_tx_entry.num_hashes, 1);
    }

    #[test]
    fn test_fixtures_data() {
        let known_good_bytes = get_known_good_entry_bytes();
        assert!(!known_good_bytes.is_empty());

        let small_entries = get_small_entries();
        assert!(!small_entries.is_empty());

        let edge_case_data = get_edge_case_data();
        assert!(!edge_case_data.is_empty());
    }

    #[test]
    fn test_test_data_loader() {
        let dataset = ShredDataset::new();
        assert_eq!(dataset.total_samples, 0);
        assert!(dataset.files.is_empty());

        let samples = get_test_samples(&dataset, 10);
        assert!(samples.is_empty());

        let edge_cases = get_edge_case_samples(&dataset);
        assert!(edge_cases.is_empty());
    }

    #[test]
    fn test_assertion_macros() {
        let data1 = vec![1, 2, 3, 4];
        let data2 = vec![1, 2, 3, 4];

        assert_binary_compatible!(data1, data2);
        assert_byte_array_equal!(data1, data2);

        let entry = generate_empty_entry();
        assert_roundtrip_compatible!(entry);
    }

    #[test]
    fn test_malformed_data_handling() {
        let malformed_samples = generate_malformed_data_samples();
        assert!(!malformed_samples.is_empty());

        for sample in malformed_samples {
            assert_no_panic(
                || {
                    let _ = bincode::deserialize::<shredstream_decoder::types::Entry>(&sample);
                },
                "Malformed data should not cause panic",
            );
        }
    }

    #[test]
    fn test_various_message_types() {
        let messages = generate_various_message_types();
        assert_eq!(messages.len(), 4);

        for message in messages {
            assert_roundtrip_compatible!(message);
        }
    }

    #[test]
    fn test_edge_case_entries() {
        let entries = generate_edge_case_entries();
        assert_eq!(entries.len(), 5);

        for entry in entries {
            assert_roundtrip_compatible!(entry);
        }
    }

    #[test]
    fn test_compatibility_result_enum() {
        let result = CompatibilityResult::Success;
        assert_eq!(result, CompatibilityResult::Success);

        let mismatch = CompatibilityResult::BinaryMismatch { custom: vec![1, 2, 3], reference: vec![1, 2, 4] };

        match mismatch {
            CompatibilityResult::BinaryMismatch { custom, reference } => {
                assert_eq!(custom, vec![1, 2, 3]);
                assert_eq!(reference, vec![1, 2, 4]);
            }
            _ => panic!("Expected BinaryMismatch"),
        }
    }

    #[test]
    fn test_generate_compatibility_report() {
        let custom_data = vec![1, 2, 3, 4];
        let reference_data = vec![1, 2, 3, 5];

        let report = generate_compatibility_report(&custom_data, &reference_data, "test");
        println!("Report: {}", report);
        assert!(report.contains("Compatibility Report for test"));
        assert!(report.contains("Custom size:"));
        assert!(report.contains("Reference size:"));
        assert!(report.contains("First difference at byte position: 20"));
    }
}
